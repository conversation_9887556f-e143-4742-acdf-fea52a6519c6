package birthdays_plugin

import (
	"context"
	"fmt"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"

	"github.com/6tail/lunar-go/calendar"
)

// 生日人员信息
type BirthdayPerson struct {
	Name       string `json:"name"`        // 姓名
	IsLunar    bool   `json:"is_lunar"`    // 是否农历生日
	Month      int    `json:"month"`       // 月份 (1-12)
	Day        int    `json:"day"`         // 日期 (1-31)
	LunarMonth int    `json:"lunar_month"` // 农历月份 (1-12，兼容旧配置)
	LunarDay   int    `json:"lunar_day"`   // 农历日期 (1-30，兼容旧配置)
}

// 生日人员配置
var birthdayPersons = []BirthdayPerson{
	{Name: "老婆", IsLunar: true, Month: 5, Day: 26},   // 农历五月二十六
	{Name: "小喜", IsLunar: true, Month: 10, Day: 28},  // 农历十月二十八
	{Name: "小希", IsLunar: true, Month: 10, Day: 30},  // 农历十月三十
	{Name: "小喜", IsLunar: false, Month: 12, Day: 5},  // 阳历12月5日
	{Name: "小希", IsLunar: false, Month: 12, Day: 14}, // 阳历12月14日
	{Name: "老妈", IsLunar: true, Month: 3, Day: 8},    // 农历三月八
}

// BirthdayReminderPlugin 生日提醒插件 - 重构版本
type BirthdayReminderPlugin struct{}

// GetStaticInfo 获取插件静态信息（仅在注册时调用一次）
func (p *BirthdayReminderPlugin) GetStaticInfo() *plugin.PluginStaticInfo {
	return &plugin.PluginStaticInfo{
		PluginID:          "birthday_reminder",
		PluginDescription: "生日提醒 - 支持农历和阳历，提前3天和当天发送提醒，钉钉使用Markdown格式，邮箱使用HTML格式",
	}
}

// GetScheduleConfig 获取插件调度配置（仅在注册时调用一次）
func (p *BirthdayReminderPlugin) GetScheduleConfig() *plugin.PluginScheduleConfig {
	return &plugin.PluginScheduleConfig{
		Schedule: types.ScheduleConfig{
			Type:     types.CronSchedule,
			CronExpr: "8 8 8 * * *", // 每天上午8点8分8秒检查
		},
		Enabled:        true,
		RunImmediately: false, // 设置为true可立即执行插件进行测试
	}
}

// GenerateContent 生成运行时内容（每次执行时调用）
func (p *BirthdayReminderPlugin) GenerateContent(ctx context.Context) (*plugin.PluginRuntimeContent, error) {
	title, dingTalkContent, emailContent := p.generateTitleAndContent()

	// 根据dingTalkContent和emailContent是否为空决定MessageTypes
	var messageTypes []types.MessageType
	if dingTalkContent != "" {
		messageTypes = append(messageTypes, types.DingTalkMessage)
	}
	if emailContent != "" {
		messageTypes = append(messageTypes, types.EmailMessage)
	}

	// 如果没有需要提醒的生日，返回空消息类型
	if len(messageTypes) == 0 {
		return &plugin.PluginRuntimeContent{
			MessageTypes: []types.MessageType{},
		}, nil
	}

	// 自定义邮件配置
	emailConfig := &types.EmailConfig{
		SMTPHost: plugin.SinaEmailConfig.SMTPHost,
		SMTPPort: plugin.SinaEmailConfig.SMTPPort,
		Username: plugin.SinaEmailConfig.Username,
		Password: plugin.SinaEmailConfig.Password,
		From:     plugin.SinaEmailConfig.From,
		FromName: plugin.SinaEmailConfig.FromName,
		To:       []string{"<EMAIL>"},
		Subject:  title,
		Body:     emailContent, // 使用HTML格式的邮箱内容
		IsHTML:   true,         // 设置为HTML格式
	}

	// 自定义钉钉配置
	dingTalkConfig := &types.DingTalkConfig{
		WebhookURL: plugin.DingTalkConfig.WebhookURL,
		Secret:     plugin.DingTalkConfig.Secret,
		AtMobiles:  []string{},
		AtAll:      false,
		Title:      title,
		Text:       dingTalkContent, // 使用Markdown格式的钉钉内容
		MsgType:    types.DingTalkMarkdown,
	}

	return &plugin.PluginRuntimeContent{
		Title:           title,
		DingTalkContent: dingTalkContent,
		EmailContent:    emailContent,
		MessageTypes:    messageTypes,
		EmailConfig:     emailConfig,
		DingTalkConfig:  dingTalkConfig,
	}, nil
}

// 获取需要提醒的生日人员（提前3天和当天）
func (p *BirthdayReminderPlugin) getBirthdayReminders() ([]BirthdayPerson, int) {
	now := time.Now()
	birthdaysByDay := make(map[int][]BirthdayPerson)

	for _, person := range birthdayPersons {
		// 标准化生日信息
		month, day, isLunar := p.normalizeBirthdayInfo(person)

		// 检查当天和未来3天的生日
		for i := 0; i <= 3; i++ {
			if p.isBirthdayMatch(now.AddDate(0, 0, i), month, day, isLunar) {
				birthdaysByDay[i] = append(birthdaysByDay[i], person)
				break // 避免重复添加同一个人
			}
		}
	}

	// 如果没有找到任何生日，返回空
	if len(birthdaysByDay) == 0 {
		return nil, -1
	}

	// 返回所有需要提醒的生日人员，按最近的日期优先
	var allReminders []BirthdayPerson
	minDaysLeft := -1

	for i := 0; i <= 3; i++ {
		if persons, exists := birthdaysByDay[i]; exists && len(persons) > 0 {
			if minDaysLeft == -1 {
				minDaysLeft = i
			}
			allReminders = append(allReminders, persons...)
		}
	}

	return allReminders, minDaysLeft
}

// 标准化生日信息（兼容旧配置格式）
func (p *BirthdayReminderPlugin) normalizeBirthdayInfo(person BirthdayPerson) (month, day int, isLunar bool) {
	month, day, isLunar = person.Month, person.Day, person.IsLunar
	// 兼容旧配置格式
	if person.LunarMonth > 0 && person.LunarDay > 0 {
		month, day, isLunar = person.LunarMonth, person.LunarDay, true
	}
	return
}

// 检查指定日期是否匹配生日
func (p *BirthdayReminderPlugin) isBirthdayMatch(checkDate time.Time, month, day int, isLunar bool) bool {
	if isLunar {
		// 农历生日检查
		checkLunar := calendar.NewSolarFromYmd(checkDate.Year(), int(checkDate.Month()), checkDate.Day()).GetLunar()
		actualMonth := checkLunar.GetMonth()

		// 如果是闰月（负数），转为正数进行比较
		// 例如：闰六月返回-6，需要转为6进行比较
		if actualMonth < 0 {
			actualMonth = -actualMonth
		}

		return actualMonth == month && checkLunar.GetDay() == day
	} else {
		// 阳历生日检查
		return int(checkDate.Month()) == month && checkDate.Day() == day
	}
}

// 生成标题
func (p *BirthdayReminderPlugin) GenerateTitle(persons []BirthdayPerson, daysLeft int) string {
	// 收集所有人的名字和生日类型
	var nameWithTypes []string
	for _, person := range persons {
		_, _, isLunar := p.normalizeBirthdayInfo(person)
		calendarType := "阳历"
		if isLunar {
			calendarType = "农历"
		}
		nameWithTypes = append(nameWithTypes, fmt.Sprintf("%s(%s)", person.Name, calendarType))
	}

	nameStr := p.formatNameList(nameWithTypes)

	if daysLeft == 0 {
		return fmt.Sprintf("🎂 %s 生日提醒!", nameStr)
	} else {
		return fmt.Sprintf("🎂 %s 生日提醒（%d天后）!", nameStr, daysLeft)
	}
}

// 格式化姓名列表
func (p *BirthdayReminderPlugin) formatNameList(names []string) string {
	switch len(names) {
	case 0:
		return ""
	case 1:
		return names[0]
	case 2:
		return fmt.Sprintf("%s、%s", names[0], names[1])
	default:
		if len(names) <= 4 {
			// 4个人以内全部列出
			result := names[0]
			for i := 1; i < len(names); i++ {
				result += "、" + names[i]
			}
			return result
		} else {
			// 超过4个人显示前3个+等X人
			return fmt.Sprintf("%s、%s、%s等%d人", names[0], names[1], names[2], len(names))
		}
	}
}

// 生成钉钉消息内容（Markdown格式）
func (p *BirthdayReminderPlugin) generateDingTalkMessage(persons []BirthdayPerson, daysLeft int) string {
	now := time.Now()
	birthdaysByDay := p.groupPersonsByDay(persons, now)

	var message string

	// 按日期顺序显示生日信息
	for i := 0; i <= 3; i++ {
		if persons, exists := birthdaysByDay[i]; exists && len(persons) > 0 {
			message += p.formatDaySection(now.AddDate(0, 0, i), i, persons)
		}
	}

	// 添加祝福语
	message += "---\n"
	message += "**祝福语**：生日快乐！愿你的每一天都充满阳光和快乐！\n"

	return message
}

// 生成邮箱消息内容（HTML格式）
func (p *BirthdayReminderPlugin) generateEmailMessage(persons []BirthdayPerson, daysLeft int) string {
	now := time.Now()
	birthdaysByDay := p.groupPersonsByDay(persons, now)

	var message string
	message += `<html>
<head>
	<meta charset="UTF-8">
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #fff; margin: 0; padding: 20px;">
	<div style="max-width: 600px; margin: 0 auto;">`

	// 按日期顺序显示生日信息
	for i := 0; i <= 3; i++ {
		if persons, exists := birthdaysByDay[i]; exists && len(persons) > 0 {
			message += p.formatDaySectionHTML(now.AddDate(0, 0, i), i, persons)
		}
	}

	// 添加祝福语
	message += `<hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0;">
	<p style="margin: 0; color: #666; font-size: 16px;">
		<strong>祝福语</strong>：生日快乐！愿你的每一天都充满阳光和快乐！
	</p>
	</div>
</body>
</html>`

	return message
}

// 按日期分组生日人员
func (p *BirthdayReminderPlugin) groupPersonsByDay(persons []BirthdayPerson, now time.Time) map[int][]BirthdayPerson {
	birthdaysByDay := make(map[int][]BirthdayPerson)

	for _, person := range persons {
		month, day, isLunar := p.normalizeBirthdayInfo(person)

		// 找到这个人的生日是第几天
		for i := 0; i <= 3; i++ {
			if p.isBirthdayMatch(now.AddDate(0, 0, i), month, day, isLunar) {
				birthdaysByDay[i] = append(birthdaysByDay[i], person)
				break
			}
		}
	}

	return birthdaysByDay
}

// 格式化某一天的生日信息（Markdown格式）
func (p *BirthdayReminderPlugin) formatDaySection(targetDate time.Time, daysOffset int, persons []BirthdayPerson) string {
	lunar := calendar.NewSolarFromYmd(targetDate.Year(), int(targetDate.Month()), targetDate.Day()).GetLunar()

	// 格式化日期显示
	weekday := []string{"周日", "周一", "周二", "周三", "周四", "周五", "周六"}[targetDate.Weekday()]
	solarDate := fmt.Sprintf("%d年%d月%d日 %s", targetDate.Year(), targetDate.Month(), targetDate.Day(), weekday)
	lunarDate := fmt.Sprintf("（农历%d月%d日）", lunar.GetMonth(), lunar.GetDay())

	// 根据天数显示不同的标题和图标
	dayTitle, dayIcon := p.getDayTitleAndIcon(daysOffset)

	message := fmt.Sprintf("### %s %s生日\n", dayIcon, dayTitle)
	message += fmt.Sprintf("> **%s** %s\n\n", solarDate, lunarDate)

	for _, person := range persons {
		month, day, isLunar := p.normalizeBirthdayInfo(person)
		calendarType, calendarIcon := p.getCalendarTypeAndIcon(isLunar)
		message += fmt.Sprintf("• 🎈 **%s** - %s %s生日（%d月%d日）\n",
			person.Name, calendarIcon, calendarType, month, day)
	}
	message += "\n"

	return message
}

// 格式化某一天的生日信息（HTML格式）
func (p *BirthdayReminderPlugin) formatDaySectionHTML(targetDate time.Time, daysOffset int, persons []BirthdayPerson) string {
	lunar := calendar.NewSolarFromYmd(targetDate.Year(), int(targetDate.Month()), targetDate.Day()).GetLunar()

	// 格式化日期显示
	weekday := []string{"周日", "周一", "周二", "周三", "周四", "周五", "周六"}[targetDate.Weekday()]
	solarDate := fmt.Sprintf("%d年%d月%d日 %s", targetDate.Year(), targetDate.Month(), targetDate.Day(), weekday)
	lunarDate := fmt.Sprintf("（农历%d月%d日）", lunar.GetMonth(), lunar.GetDay())

	// 根据天数显示不同的标题和图标
	dayTitle, dayIcon := p.getDayTitleAndIcon(daysOffset)

	var message string
	// 类似Markdown的简洁样式
	message += fmt.Sprintf(`<h3 style="color: #333; font-size: 18px; margin: 20px 0 10px 0;">%s %s生日</h3>`, dayIcon, dayTitle)

	// 引用块样式显示日期信息
	message += `<blockquote style="margin: 0 0 15px 0; padding: 10px 15px; background: #f8f9fa; border-left: 4px solid #ddd; color: #666;">`
	message += fmt.Sprintf(`<strong>%s</strong>%s`, solarDate, lunarDate)
	message += `</blockquote>`

	// 生日人员列表 - 类似Markdown的列表样式
	for _, person := range persons {
		month, day, isLunar := p.normalizeBirthdayInfo(person)
		calendarType, calendarIcon := p.getCalendarTypeAndIcon(isLunar)

		message += fmt.Sprintf(`<p style="margin: 8px 0; color: #333;">• 🎈 <strong>%s</strong> - %s %s生日（%d月%d日）</p>`,
			person.Name, calendarIcon, calendarType, month, day)
	}
	message += `<br>`

	return message
}

// 获取日期标题和图标
func (p *BirthdayReminderPlugin) getDayTitleAndIcon(daysOffset int) (string, string) {
	switch daysOffset {
	case 0:
		return "今天", "🎉"
	case 1:
		return "明天", "⏰"
	default:
		return fmt.Sprintf("%d天后", daysOffset), "📅"
	}
}

// 获取日历类型和图标
func (p *BirthdayReminderPlugin) getCalendarTypeAndIcon(isLunar bool) (string, string) {
	if isLunar {
		return "农历", "🌙"
	}
	return "阳历", "☀️"
}

// 生成标题和不同格式的内容
func (p *BirthdayReminderPlugin) generateTitleAndContent() (title, dingTalkContent, emailContent string) {
	// 获取需要提醒的生日人员
	reminders, daysLeft := p.getBirthdayReminders()

	// 如果没有需要提醒的生日，返回空内容
	if len(reminders) == 0 {
		return "", "", ""
	}

	// 生成标题和不同格式的内容
	title = p.GenerateTitle(reminders, daysLeft)
	dingTalkContent = p.generateDingTalkMessage(reminders, daysLeft)
	emailContent = p.generateEmailMessage(reminders, daysLeft)
	return title, dingTalkContent, emailContent
}
