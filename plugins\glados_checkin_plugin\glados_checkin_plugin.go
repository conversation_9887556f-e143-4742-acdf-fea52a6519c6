package glados_checkin_plugin

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"
)

// GladosCheckinPlugin GLaDOS签到插件 - 重构版本
type GladosCheckinPlugin struct{}

// GetStaticInfo 获取插件静态信息（仅在注册时调用一次）
func (p *GladosCheckinPlugin) GetStaticInfo() *plugin.PluginStaticInfo {
	return &plugin.PluginStaticInfo{
		PluginID:          "glados_checkin",
		PluginDescription: "GLaDOS每日签到插件 - 每天9点自动签到并发送结果通知",
	}
}

// GetScheduleConfig 获取插件调度配置（仅在注册时调用一次）
func (p *GladosCheckinPlugin) GetScheduleConfig() *plugin.PluginScheduleConfig {
	return &plugin.PluginScheduleConfig{
		Schedule: types.ScheduleConfig{
			Type:     types.CronSchedule,
			CronExpr: "0 5 9 * * *", // 每天9点执行一次
		},
		Enabled:        true,
		RunImmediately: false, // 设置为true可立即执行插件进行测试
	}
}

// GenerateContent 生成运行时内容（每次执行时调用）
func (p *GladosCheckinPlugin) GenerateContent(ctx context.Context) (*plugin.PluginRuntimeContent, error) {
	title, dingTalkContent, emailContent := p.generateTitleAndContent()

	// 根据content是否为空决定MessageTypes
	var messageTypes []types.MessageType
	if dingTalkContent != "" {
		messageTypes = append(messageTypes, types.DingTalkMessage)
	}
	if emailContent != "" {
		messageTypes = append(messageTypes, types.EmailMessage)
	}

	// 如果没有内容，返回空消息类型
	if len(messageTypes) == 0 {
		return &plugin.PluginRuntimeContent{
			MessageTypes: []types.MessageType{},
		}, nil
	}

	// 自定义邮件配置
	emailConfig := &types.EmailConfig{
		SMTPHost: plugin.SinaEmailConfig.SMTPHost,
		SMTPPort: plugin.SinaEmailConfig.SMTPPort,
		Username: plugin.SinaEmailConfig.Username,
		Password: plugin.SinaEmailConfig.Password,
		From:     plugin.SinaEmailConfig.From,
		FromName: plugin.SinaEmailConfig.FromName,
		To:       []string{"<EMAIL>"}, // 收件人
		Subject:  title,                     // 邮件标题
		Body:     emailContent,              // 邮件内容
		IsHTML:   true,                      // 使用HTML格式
	}

	// 自定义钉钉配置
	dingTalkConfig := &types.DingTalkConfig{
		WebhookURL: plugin.DingTalkConfig.WebhookURL,
		Secret:     plugin.DingTalkConfig.Secret,
		AtMobiles:  []string{},
		AtAll:      false,
		Title:      title,                  // 钉钉标题
		Text:       dingTalkContent,        // 钉钉内容
		MsgType:    types.DingTalkMarkdown, // 使用Markdown格式
	}

	return &plugin.PluginRuntimeContent{
		Title:           title,
		DingTalkContent: dingTalkContent,
		EmailContent:    emailContent,
		MessageTypes:    messageTypes,
		EmailConfig:     emailConfig,
		DingTalkConfig:  dingTalkConfig,
	}, nil
}

// generateTitleAndContent 生成标题和内容
func (p *GladosCheckinPlugin) generateTitleAndContent() (title, dingTalkContent, emailContent string) {
	// 获取环境变量
	cookie := "koa:sess=eyJ1c2VySWQiOjM1MjkyOSwiX2V4cGlyZSI6MTc3OTY2ODgxOTE1OSwiX21heEFnZSI6MjU5MjAwMDAwMDB9; koa:sess.sig=68P-yOLOMgnRSQBbbhGUah73QTI"

	// 执行签到
	checkinTime := time.Now().Format("2006-01-02 15:04:05")
	result := p.gladosCheckin(cookie)

	// 准备通知内容
	if result.Success {
		title = "🎉 Glados 签到成功 "
		dingTalkContent = fmt.Sprintf(`**签到时间**: %s

**签到结果**: ✅ 签到成功

**账户信息**:
- 剩余流量: %s
- 账户状态: %s
- 到期时间: %s

---
*🤖 自动签到服务 - 每日9点执行*`,
			checkinTime,
			result.LeftDays,
			result.Status,
			result.ExpireTime)

		emailContent = ""
		// 		emailContent = fmt.Sprintf(`
		// <h2>🎉 GLaDOS 签到成功</h2>
		// <p><strong>签到时间</strong>: %s</p>
		// <p><strong>签到结果</strong>: ✅ 签到成功</p>

		// <h3>📊 账户信息</h3>
		// <ul>
		// <li><strong>剩余流量</strong>: %s</li>
		// <li><strong>账户状态</strong>: %s</li>
		// <li><strong>到期时间</strong>: %s</li>
		// </ul>

		// <hr>
		// <p><em>🤖 自动签到服务 - 每日9点执行</em></p>
		// `, checkinTime, result.LeftDays, result.Status, result.ExpireTime)
	} else {
		title = "❌ Glados 签到失败"
		dingTalkContent = fmt.Sprintf(`**签到时间**: %s

**签到结果**: ❌ 签到失败

**错误信息**: %s

---
*🤖 自动签到服务 - 请检查配置*`, checkinTime, result.Message)

		emailContent = ""
		// 		emailContent = fmt.Sprintf(`
		// <h2>❌ GLaDOS 签到失败</h2>
		// <p><strong>签到时间</strong>: %s</p>
		// <p><strong>签到结果</strong>: ❌ 签到失败</p>
		// <p><strong>错误信息</strong>: %s</p>

		// <hr>
		// <p><em>🤖 自动签到服务 - 请检查配置</em></p>
		// `, checkinTime, result.Message)
	}

	return title, dingTalkContent, emailContent
}

// CheckinResult 签到结果结构
type CheckinResult struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	LeftDays   string `json:"leftDays"`
	Status     string `json:"status"`
	ExpireTime string `json:"expireTime"`
}

// gladosCheckin 执行GLaDOS签到
func (p *GladosCheckinPlugin) gladosCheckin(cookie string) CheckinResult {
	// 签到请求
	checkinURL := "https://glados.rocks/api/user/checkin"
	checkinData := map[string]string{"token": "glados.one"}

	jsonData, _ := json.Marshal(checkinData)
	req, err := http.NewRequest("POST", checkinURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("创建签到请求失败: %v", err)}
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cookie", cookie)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("签到请求失败: %v", err)}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("读取签到响应失败: %v", err)}
	}

	var checkinResp map[string]interface{}
	if err := json.Unmarshal(body, &checkinResp); err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("解析签到响应失败: %v", err)}
	}

	// 获取用户状态
	statusURL := "https://glados.rocks/api/user/status"
	statusReq, err := http.NewRequest("GET", statusURL, nil)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("创建状态请求失败: %v", err)}
	}

	statusReq.Header.Set("Cookie", cookie)
	statusReq.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	statusResp, err := client.Do(statusReq)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("状态请求失败: %v", err)}
	}
	defer statusResp.Body.Close()

	statusBody, err := io.ReadAll(statusResp.Body)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("读取状态响应失败: %v", err)}
	}

	var statusData map[string]interface{}
	if err := json.Unmarshal(statusBody, &statusData); err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("解析状态响应失败: %v", err)}
	}

	// 解析响应数据
	if data, ok := statusData["data"].(map[string]interface{}); ok {
		leftDays := "未知"
		status := "未知"
		expireTime := "未知"

		if ld, ok := data["leftDays"].(string); ok {
			leftDays = ld
		}
		if st, ok := data["vip"].(bool); ok {
			if st {
				status = "VIP用户"
			} else {
				status = "普通用户"
			}
		}
		if et, ok := data["days"].(float64); ok {
			expireTime = fmt.Sprintf("%.0f天后", et)
		}

		return CheckinResult{
			Success:    true,
			Message:    "签到成功",
			LeftDays:   leftDays,
			Status:     status,
			ExpireTime: expireTime,
		}
	}

	return CheckinResult{Success: false, Message: "解析用户状态失败"}
}
