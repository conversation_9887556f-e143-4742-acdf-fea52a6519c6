package examples

import (
	"context"
	"fmt"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"
)

// WeatherReminderPlugin 天气提醒插件 - 展示条件性消息发送
type WeatherReminderPlugin struct{}

func (p *WeatherReminderPlugin) GetStaticInfo() *plugin.PluginStaticInfo {
	return &plugin.PluginStaticInfo{
		PluginID:          "weather_reminder",
		PluginDescription: "天气提醒插件 - 重构版本（条件性发送）",
	}
}

func (p *WeatherReminderPlugin) GetScheduleConfig() *plugin.PluginScheduleConfig {
	return &plugin.PluginScheduleConfig{
		Schedule: types.ScheduleConfig{
			Type:     types.CronSchedule,
			CronExpr: "0 0 7 * * *", // 每天早上7点执行
		},
		Enabled:        true,
		RunImmediately: false,
	}
}

func (p *WeatherReminderPlugin) GenerateContent(ctx context.Context) (*plugin.PluginRuntimeContent, error) {
	// 模拟获取天气信息
	weather := p.getWeatherInfo()

	// 只有在特殊天气情况下才发送提醒
	if weather.NeedAlert {
		title := fmt.Sprintf("🌤️ 天气提醒 - %s", weather.Condition)

		dingTalkContent := fmt.Sprintf(`### 🌤️ 天气提醒

**当前天气**: %s
**温度**: %s
**提醒**: %s

---
*🤖 天气监控服务*`, weather.Condition, weather.Temperature, weather.Alert)

		emailContent := fmt.Sprintf(`
<h2>🌤️ 天气提醒</h2>
<p><strong>当前天气</strong>: %s</p>
<p><strong>温度</strong>: %s</p>
<p><strong>提醒</strong>: %s</p>
<hr>
<p><em>🤖 天气监控服务</em></p>
`, weather.Condition, weather.Temperature, weather.Alert)

		return &plugin.PluginRuntimeContent{
			Title:           title,
			DingTalkContent: dingTalkContent,
			EmailContent:    emailContent,
			MessageTypes:    []types.MessageType{types.DingTalkMessage},
		}, nil
	}

	// 天气正常，不发送消息
	return &plugin.PluginRuntimeContent{
		MessageTypes: []types.MessageType{},
	}, nil
}

// WeatherInfo 天气信息结构
type WeatherInfo struct {
	Condition   string
	Temperature string
	Alert       string
	NeedAlert   bool
}

// getWeatherInfo 模拟获取天气信息
func (p *WeatherReminderPlugin) getWeatherInfo() WeatherInfo {
	// 模拟天气数据
	// 在实际应用中，这里会调用天气API
	now := time.Now()

	// 模拟：周末不发送提醒
	if now.Weekday() == time.Saturday || now.Weekday() == time.Sunday {
		return WeatherInfo{
			Condition:   "晴天",
			Temperature: "22°C",
			Alert:       "",
			NeedAlert:   false,
		}
	}

	// 模拟：工作日有概率发送天气提醒
	if now.Hour() == 7 && now.Minute() < 30 {
		return WeatherInfo{
			Condition:   "雨天",
			Temperature: "18°C",
			Alert:       "今日有雨，记得带伞！",
			NeedAlert:   true,
		}
	}

	return WeatherInfo{
		Condition:   "多云",
		Temperature: "25°C",
		Alert:       "",
		NeedAlert:   false,
	}
}
